{% extends 'tapemanagement/base.html' %}

{% block title %}Pending Requests - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    /* ONGC Color Scheme */
    :root {
        --ongc-primary: #1e3a8a;      /* ONGC Blue */
        --ongc-secondary: #dc2626;     /* ONGC Red */
        --ongc-accent: #f59e0b;        /* ONGC Orange/Yellow */
        --ongc-light-blue: #dbeafe;    /* Light blue background */
        --ongc-dark-blue: #1e40af;     /* Darker blue for text */
        --ongc-gray: #6b7280;          /* Professional gray */
        --ongc-light-gray: #f9fafb;    /* Very light gray */
        --ongc-success: #059669;       /* Green for success */
        --ongc-warning: #d97706;       /* Orange for warning */
    }

    /* Simple Table Design */
    .requests-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(30, 58, 138, 0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .table-header {
        background: linear-gradient(135deg, var(--ongc-primary), var(--ongc-dark-blue));
        color: white;
        padding: 20px;
        text-align: center;
    }

    .table-header h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .simple-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .simple-table th {
        background: var(--ongc-light-blue);
        color: var(--ongc-dark-blue);
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid var(--ongc-primary);
    }

    .simple-table td {
        padding: 15px 12px;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: top;
    }

    .simple-table tr:hover {
        background-color: var(--ongc-light-gray);
    }

    /* Request ID Styling */
    .request-id {
        font-weight: bold;
        color: var(--ongc-primary);
        font-size: 1.1em;
    }

    /* Status Badges */
    .status-pending {
        background: var(--ongc-warning);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: 500;
    }

    .status-approved {
        background: var(--ongc-success);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: 500;
    }

    .status-rejected {
        background: var(--ongc-secondary);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: 500;
    }

    .status-completed {
        background: var(--ongc-primary);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: 500;
    }

    /* Simple Info Display */
    .info-row {
        margin-bottom: 8px;
    }

    .info-label {
        font-weight: 600;
        color: var(--ongc-dark-blue);
        display: inline-block;
        min-width: 80px;
    }

    .info-value {
        color: var(--ongc-gray);
    }

    /* Media Info Box */
    .media-box {
        background: var(--ongc-light-blue);
        padding: 10px;
        border-radius: 6px;
        border-left: 4px solid var(--ongc-primary);
        margin: 8px 0;
    }

    .media-title {
        font-weight: 600;
        color: var(--ongc-primary);
        margin-bottom: 5px;
    }

    /* Action Buttons */
    .btn-ongc-primary {
        background: var(--ongc-primary);
        border: none;
        color: white;
        padding: 6px 15px;
        border-radius: 6px;
        font-size: 0.9em;
        text-decoration: none;
        display: inline-block;
        margin-right: 5px;
        transition: background 0.3s ease;
    }

    .btn-ongc-primary:hover {
        background: var(--ongc-dark-blue);
        color: white;
        text-decoration: none;
    }

    .btn-ongc-secondary {
        background: var(--ongc-secondary);
        border: none;
        color: white;
        padding: 6px 15px;
        border-radius: 6px;
        font-size: 0.9em;
        text-decoration: none;
        display: inline-block;
        margin-right: 5px;
    }

    .btn-ongc-secondary:hover {
        background: #b91c1c;
        color: white;
        text-decoration: none;
    }

    /* File Count Badge */
    .file-badge {
        background: var(--ongc-accent);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.8em;
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .simple-table {
            font-size: 12px;
        }

        .simple-table th,
        .simple-table td {
            padding: 10px 8px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4" style="background: linear-gradient(135deg, var(--ongc-primary), var(--ongc-dark-blue)); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);">
    <h2 style="margin: 0; font-weight: 600;">
        <i class="fas fa-clock" style="color: var(--ongc-accent);"></i> Pending Requests
    </h2>
    <a href="{% url 'request_form' %}" class="btn-ongc-secondary" style="padding: 10px 20px; font-weight: 500;">
        <i class="fas fa-plus"></i> New Request
    </a>
</div>

<!-- Search and Filter -->
<div class="requests-table mb-4">
    <div style="background: var(--ongc-light-blue); padding: 20px; border-radius: 12px 12px 0 0; border-bottom: 2px solid var(--ongc-primary);">
        <h5 style="color: var(--ongc-primary); margin-bottom: 15px; font-weight: 600;">
            <i class="fas fa-search"></i> Search & Filter Requests
        </h5>
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text"
                       class="form-control"
                       name="search"
                       placeholder="Search by Survey, Area, or User..."
                       value="{{ search_query|default:'' }}"
                       style="border: 2px solid var(--ongc-primary); border-radius: 8px;">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-control" style="border: 2px solid var(--ongc-primary); border-radius: 8px;">
                    <option value="">All Status</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn-ongc-primary" style="width: 100%;">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
            <div class="col-md-3">
                <a href="{% url 'pending_requests' %}" class="btn-ongc-secondary" style="width: 100%; text-align: center;">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Simple ONGC-styled Table -->
{% if page_obj %}
<div class="requests-table">
    <!-- Table Header -->
    <div class="table-header">
        <h3><i class="fas fa-list-alt"></i> Your Requests</h3>
    </div>

    <!-- Table Content -->
    <div class="table-responsive">
        <table class="simple-table">
            <thead>
                <tr>
                    <th style="width: 8%;">Request ID</th>
                    <th style="width: 20%;">Survey Details</th>
                    <th style="width: 15%;">User Info</th>
                    <th style="width: 20%;">Media Information</th>
                    <th style="width: 12%;">Status</th>
                    <th style="width: 10%;">Files</th>
                    <th style="width: 15%;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for request in page_obj %}
                <tr>
                    <!-- Request ID -->
                    <td>
                        <div class="request-id">#{{ request.id }}</div>
                        <small style="color: #6b7280;">{{ request.created_at|date:"M d, Y" }}</small>
                    </td>

                    <!-- Survey Details -->
                    <td>
                        <div class="info-row">
                            <span class="info-label">Survey:</span>
                            <span class="info-value">{{ request.survey_name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Area:</span>
                            <span class="info-value">{{ request.area }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Type:</span>
                            <span class="info-value">{{ request.get_datatype_display }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Group:</span>
                            <span class="info-value">{{ request.group }}</span>
                        </div>
                    </td>

                    <!-- User Info -->
                    <td>
                        <div class="info-row">
                            <span class="info-label">Name:</span>
                            <span class="info-value">{{ request.username }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">CPF:</span>
                            <span class="info-value">{{ request.cpf_number }}</span>
                        </div>
                        {% if request.contractor %}
                        <div class="info-row">
                            <span class="info-label">Contractor:</span>
                            <span class="info-value">{{ request.contractor }}</span>
                        </div>
                        {% endif %}
                    </td>

                    <!-- Media Information -->
                    <td>
                        <div class="media-box">
                            <div class="media-title">Input Media</div>
                            <div style="font-size: 0.9em;">{{ request.get_input_media_display }}</div>
                            {% if request.input_vendor_id %}
                                <div style="font-size: 0.8em; color: #6b7280;">ID: {{ request.input_vendor_id }}</div>
                            {% endif %}
                        </div>
                        <div class="media-box">
                            <div class="media-title">Output Media</div>
                            <div style="font-size: 0.9em;">{{ request.get_output_media_display }}</div>
                            {% if request.output_vendor_id %}
                                <div style="font-size: 0.8em; color: #6b7280;">ID: {{ request.output_vendor_id }}</div>
                            {% endif %}
                        </div>
                    </td>

                    <!-- Status -->
                    <td style="text-align: center;">
                        <span class="status-{{ request.status }}">
                            {{ request.get_status_display }}
                        </span>
                        <br>
                        <small style="color: #6b7280;">{{ request.updated_at|date:"M d" }}</small>
                    </td>

                    <!-- Files -->
                    <td style="text-align: center;">
                        {% if request.uploads.all %}
                            <span class="file-badge">
                                {{ request.uploads.count }} file{{ request.uploads.count|pluralize }}
                            </span>
                            {% for upload in request.uploads.all|slice:":2" %}
                                <br><small style="color: #6b7280; font-size: 0.8em;">
                                    <i class="fas fa-file-{% if upload.filename|slice:'-4:' == '.pdf' %}pdf{% else %}alt{% endif %}"></i>
                                    {{ upload.filename|truncatechars:15 }}
                                </small>
                            {% endfor %}
                            {% if request.uploads.count > 2 %}
                                <br><small style="color: #6b7280;">+{{ request.uploads.count|add:"-2" }} more</small>
                            {% endif %}
                        {% else %}
                            <span style="color: #9ca3af; font-size: 0.9em;">No files</span>
                        {% endif %}
                    </td>

                    <!-- Actions -->
                    <td>
                        <!-- Upload File Button -->
                        {% if request.status == 'pending' or request.status == 'approved' %}
                        <a href="{% url 'upload_file' request.id %}" class="btn-ongc-primary">
                            <i class="fas fa-upload"></i> Upload
                        </a>
                        <br><br>
                        {% endif %}

                        <!-- Admin Status Update -->
                        {% if user.is_superuser and request.status == 'pending' %}
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" style="font-size: 0.8em;">
                                <i class="fas fa-cog"></i> Manage
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="approved">
                                        <button type="submit" class="dropdown-item text-success">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="rejected">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="completed">
                                        <button type="submit" class="dropdown-item text-info">
                                            <i class="fas fa-check-circle"></i> Complete
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5>No requests found</h5>
        <p class="text-muted">You haven't submitted any requests yet.</p>
        <a href="{% url 'request_form' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Submit Your First Request
        </a>
    </div>
</div>
{% endif %}
{% endblock %}
