{% extends 'tapemanagement/base.html' %}

{% block title %}Completed Requests - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    /* Simple Table Styling */
    .simple-requests-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .table-title {
        background: #800000;
        color: white;
        padding: 15px 20px;
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .simple-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .simple-table th {
        background: #f8f9fa;
        color: #495057;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #800000;
        font-size: 0.9rem;
    }

    .simple-table td {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        vertical-align: top;
        font-size: 0.9rem;
    }

    .simple-table tr:hover {
        background-color: #f8f9fa;
    }

    /* Request ID styling */
    .request-id {
        font-weight: bold;
        color: #000000;
        font-size: 1.1rem;
    }

    /* Info rows for organized display */
    .info-item {
        margin-bottom: 5px;
        line-height: 1.4;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        display: inline-block;
        min-width: 70px;
    }

    .info-value {
        color: #6c757d;
    }

    /* Status badges */
    .status-pending { background: #ffc107; color: #000; }
    .status-approved { background: #28a745; color: #fff; }
    .status-rejected { background: #dc3545; color: #fff; }
    .status-completed { background: #17a2b8; color: #fff; }

    .status-badge {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
        display: inline-block;
    }

    /* Media info box */
    .media-box {
        background: #e3f2fd;
        padding: 8px;
        border-radius: 5px;
        margin: 3px 0;
        border-left: 3px solid #2196f3;
    }

    .media-title {
        font-weight: 600;
        color: #1976d2;
        font-size: 0.8rem;
        margin-bottom: 3px;
    }

    /* File badge */
    .file-count {
        background: #ff9800;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .simple-table {
            font-size: 0.8rem;
        }

        .simple-table th,
        .simple-table td {
            padding: 8px;
        }

        .info-label {
            min-width: 50px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-check-circle"></i> Completed Requests</h2>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <h6 class="card-title mb-3">
            <i class="fas fa-search"></i> Search & Filter Completed Requests
        </h6>
        <form method="get" class="row g-3">
            <!-- Text Search -->
            <div class="col-md-4">
                <label class="form-label">Search Text</label>
                <input type="text"
                       class="form-control"
                       name="search"
                       placeholder="Search by Survey, Area, User, CPF, Group..."
                       value="{{ search_query|default:'' }}">
            </div>

            <!-- Date Range -->
            <div class="col-md-3">
                <label class="form-label">From Date</label>
                <input type="date"
                       class="form-control"
                       name="from_date"
                       value="{{ from_date|default:'' }}">
            </div>

            <div class="col-md-3">
                <label class="form-label">To Date</label>
                <input type="date"
                       class="form-control"
                       name="to_date"
                       value="{{ to_date|default:'' }}">
            </div>

            <!-- Action Buttons -->
            <div class="col-md-2 d-flex align-items-end">
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'completed_requests' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>

        <!-- Search Info -->
        {% if search_query or from_date or to_date %}
        <div class="mt-3">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Showing results for:
                {% if search_query %}
                    Text: "<strong>{{ search_query }}</strong>"
                {% endif %}
                {% if from_date %}
                    From: <strong>{{ from_date }}</strong>
                {% endif %}
                {% if to_date %}
                    To: <strong>{{ to_date }}</strong>
                {% endif %}
            </small>
        </div>
        {% endif %}
    </div>
</div>

<!-- Simple Organized Table -->
{% if page_obj %}
<div class="simple-requests-table">
    <!-- Table Title -->
    <h4 class="table-title">
        <i class="fas fa-check-circle"></i> Completed Requests ({{ page_obj.paginator.count }} total)
    </h4>

    <!-- Table -->
    <div class="table-responsive">
        <table class="simple-table">
            <thead>
                <tr>
                    <th style="width: 10%;">Request ID</th>
                    <th style="width: 25%;">Survey Information</th>
                    <th style="width: 20%;">User Details</th>
                    <th style="width: 20%;">Media Information</th>
                    <th style="width: 10%;">Status</th>
                    <th style="width: 8%;">Files</th>
                    <th style="width: 7%;">Completed</th>
                </tr>
            </thead>
            <tbody>
                {% for request in page_obj %}
                <tr>
                    <!-- Request ID Column -->
                    <td>
                        <div class="request-id">#{{ request.id }}</div>
                        <small style="color: #6c757d;">{{ request.created_at|date:"M d, Y" }}</small>
                    </td>

                    <!-- Survey Information Column -->
                    <td>
                        <div class="info-item">
                            <span class="info-label">Survey:</span>
                            <span class="info-value">{{ request.survey_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Area:</span>
                            <span class="info-value">{{ request.area }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Type:</span>
                            <span class="info-value">{{ request.get_datatype_display }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date:</span>
                            <span class="info-value">{{ request.date|date:"M d, Y" }}</span>
                        </div>
                    </td>

                    <!-- User Details Column -->
                    <td>
                        <div class="info-item">
                            <span class="info-label">Name:</span>
                            <span class="info-value">{{ request.username }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">CPF:</span>
                            <span class="info-value">{{ request.cpf_number }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Group:</span>
                            <span class="info-value">{{ request.group }}</span>
                        </div>
                        {% if request.contractor %}
                        <div class="info-item">
                            <span class="info-label">Contractor:</span>
                            <span class="info-value">{{ request.contractor }}</span>
                        </div>
                        {% endif %}
                    </td>

                    <!-- Media Information Column -->
                    <td>
                        <div class="media-box">
                            <div class="media-title">Input Media</div>
                            <div>{{ request.get_input_media_display }}</div>
                            {% if request.input_vendor_id %}
                                <small>ID: {{ request.input_vendor_id }}</small>
                            {% endif %}
                        </div>
                        <div class="media-box">
                            <div class="media-title">Output Media</div>
                            <div>{{ request.get_output_media_display }}</div>
                            {% if request.output_vendor_id %}
                                <small>ID: {{ request.output_vendor_id }}</small>
                            {% endif %}
                        </div>
                    </td>

                    <!-- Status Column -->
                    <td style="text-align: center;">
                        <span class="status-badge status-{{ request.status }}">
                            {{ request.get_status_display }}
                        </span>
                        <br><br>
                        <small style="color: #6c757d;">{{ request.updated_at|date:"M d" }}</small>
                    </td>

                    <!-- Files Column -->
                    <td style="text-align: center;">
                        {% if request.uploads.all %}
                            <span class="file-count">
                                {{ request.uploads.count }}
                            </span>
                            <br>
                            <small style="color: #6c757d;">file{{ request.uploads.count|pluralize }}</small>
                        {% else %}
                            <small style="color: #9ca3af;">No files</small>
                        {% endif %}
                    </td>

                    <!-- Completed Date Column -->
                    <td style="text-align: center;">
                        <strong style="color: #28a745;">{{ request.updated_at|date:"M d, Y" }}</strong>
                        <br>
                        <small style="color: #6c757d;">{{ request.updated_at|date:"H:i" }}</small>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
        <h5>No completed requests found</h5>
        <p class="text-muted">You don't have any completed requests yet.</p>
    </div>
</div>
{% endif %}
{% endblock %}
