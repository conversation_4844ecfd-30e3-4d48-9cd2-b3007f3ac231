{% extends 'tapemanagement/base.html' %}

{% block title %}Upload File - SPIC TDMS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-upload"></i> Upload File for Request #{{ tape_request.id }}</h4>
            </div>
            <div class="card-body">
                <!-- Request Details -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Request Details:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Survey:</strong> {{ tape_request.survey_name }}</p>
                            <p><strong>Area:</strong> {{ tape_request.area }}</p>
                            <p><strong>User:</strong> {{ tape_request.username }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Data Type:</strong> {{ tape_request.get_datatype_display }}</p>
                            <p><strong>Group:</strong> {{ tape_request.group }}</p>
                            <p><strong>Status:</strong>
                                <span class="badge bg-{% if tape_request.status == 'pending' %}warning{% elif tape_request.status == 'approved' %}success{% elif tape_request.status == 'rejected' %}danger{% else %}info{% endif %}">
                                    {{ tape_request.get_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Media Information -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; border-left: 3px solid #2196f3;">
                                <strong style="color: #1976d2;">Input Media:</strong><br>
                                {{ tape_request.get_input_media_display }}
                                {% if tape_request.input_vendor_id %}
                                    <br><small><strong>Input Vendor ID:</strong> {{ tape_request.input_vendor_id }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; border-left: 3px solid #2196f3;">
                                <strong style="color: #1976d2;">Output Media:</strong><br>
                                {{ tape_request.get_output_media_display }}
                                {% if tape_request.output_vendor_id %}
                                    <br><small><strong>Output Vendor ID:</strong> {{ tape_request.output_vendor_id }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Form -->
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.file.id_for_label }}" class="form-label">
                            <i class="fas fa-file"></i> Select File
                        </label>
                        {{ form.file }}
                        {% if form.file.errors %}
                            <div class="text-danger">{{ form.file.errors }}</div>
                        {% endif %}
                        <div class="form-text">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            Only .txt and .pdf files are allowed. Maximum file size: 10MB.
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'pending_requests' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Back to Requests
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Upload File
                        </button>
                    </div>
                </form>

                <!-- Existing Files -->
                {% if tape_request.uploads.all %}
                <hr>
                <h6><i class="fas fa-folder"></i> Previously Uploaded Files:</h6>
                <div class="list-group">
                    {% for upload in tape_request.uploads.all %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-{% if upload.filename|slice:'-4:' == '.pdf' %}pdf text-danger{% else %}alt text-primary{% endif %}"></i>
                            <strong>{{ upload.filename }}</strong>
                            <br>
                            <small class="text-muted">
                                Uploaded by {{ upload.uploaded_by.username }} on {{ upload.uploaded_at|date:"M d, Y H:i" }}
                            </small>
                        </div>
                        <span class="badge bg-success">
                            <i class="fas fa-check"></i> Uploaded
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> File Upload Guidelines:</h6>
            <ul class="mb-0">
                <li>Supported file formats: .txt, .pdf</li>
                <li>Maximum file size: 10MB per file</li>
                <li>You can upload multiple files for the same request</li>
                <li>Files should contain relevant documentation or data related to your request</li>
                <li>Ensure file names are descriptive and professional</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.querySelector('input[type="file"]');
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Check file size (10MB = 10 * 1024 * 1024 bytes)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size exceeds 10MB limit. Please choose a smaller file.');
                e.target.value = '';
                return;
            }
            
            // Check file extension
            const allowedExtensions = ['.txt', '.pdf'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
            
            if (!allowedExtensions.includes(fileExtension)) {
                alert('Only .txt and .pdf files are allowed.');
                e.target.value = '';
                return;
            }
        }
    });
});
</script>
{% endblock %}
