{% extends 'tapemanagement/base.html' %}

{% block title %}Dashboard - SPIC TDMS{% endblock %}

{% block content %}
<div class="header-title">
    WELCOME TO SPIC TAPE DATA MANAGEMENT SYSTEM
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-user"></i> Welcome, {{ user.username }}!
                </h5>
                <p class="card-text">
                    <strong>CPF:</strong> {{ user.cpf_number }}<br>
                    {% if user.is_superuser %}
                        <span class="badge bg-warning">Administrator</span>
                    {% else %}
                        <span class="badge bg-info">User</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ pending_count }}</h4>
                        <p class="card-text">Pending Requests</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ completed_count }}</h4>
                        <p class="card-text">Completed Requests</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_requests }}</h4>
                        <p class="card-text">Total Requests</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Action Cards -->
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Request</h5>
                <p class="card-text">
                    Submit a new tape library request for data processing and management.
                </p>
                <a href="{% url 'request_form' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Request
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                <h5 class="card-title">Pending</h5>
                <p class="card-text">
                    View and manage your submitted requests. Upload supporting documents and track status.
                </p>
                <a href="{% url 'pending_requests' %}" class="btn btn-warning">
                    <i class="fas fa-eye"></i> View Pending
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="card-title">Completed</h5>
                <p class="card-text">
                    Review your approved and completed requests with their final status and documentation.
                </p>
                <a href="{% url 'completed_requests' %}" class="btn btn-success">
                    <i class="fas fa-check"></i> View Completed
                </a>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    <em>"SeisData Processing and Interpretation Centre at NBP Green Height, Mumbai is one of the Seismic Data Processing centres of ONGC. The Centre processes huge volume of Seismic data using state-of-the-art Enterprise Software solutions. The acquired Seismic Data are received from the Operational fields mostly the Western Offshore Basin through various Tape Cartridges(3590/3592/LTO etc) and these Cartridges are managed at SPIC, TDMS. This portal has been developed to ensure secure and efficient Tape management using TS3500(E06,E07 drives),TS4500(E08,60f drives) and LTO drives as well as to accurately record and track tape shipments to various Centres and Locations."</em>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
