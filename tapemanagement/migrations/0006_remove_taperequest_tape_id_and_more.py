# Generated by Django 4.2.7 on 2025-06-02 09:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tapemanagement', '0005_taperequest_input_vendor_id_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='taperequest',
            name='tape_id',
        ),
        migrations.AlterField(
            model_name='taperequest',
            name='activity',
            field=models.CharField(max_length=250),
        ),
        migrations.AlterField(
            model_name='taperequest',
            name='datatype',
            field=models.CharField(choices=[('2D', '2D'), ('3D', '3D')], max_length=10),
        ),
        migrations.AlterField(
            model_name='taperequest',
            name='input_vendor_id',
            field=models.CharField(default='DEFAULT', max_length=30),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='taperequest',
            name='output_vendor_id',
            field=models.Cha<PERSON><PERSON><PERSON>(default='DEFAULT', max_length=30),
            preserve_default=False,
        ),
    ]
