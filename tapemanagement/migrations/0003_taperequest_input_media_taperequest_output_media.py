# Generated by Django 4.2.7 on 2025-06-02 07:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tapemanagement', '0002_alter_taperequest_acquisition_year'),
    ]

    operations = [
        migrations.AddField(
            model_name='taperequest',
            name='input_media',
            field=models.CharField(choices=[('3592_JA', '3592 - JA'), ('3592_JB', '3592 - JB'), ('3592_JC', '3592 - JC'), ('3592_JD', '3592 - JD'), ('3592_JE', '3592 - JE'), ('LTO_1', 'LTO - 1'), ('LTO_2', 'LTO - 2'), ('LTO_3', 'LTO - 3'), ('LTO_4', 'LTO - 4'), ('LTO_5', 'LTO - 5'), ('LTO_6', 'LTO - 6'), ('LTO_7', 'LTO - 7'), ('LTO_8', 'LTO - 8'), ('LTO_9', 'LTO - 9'), ('DISK_SPIC_LFS', 'Disk Storage - SPIC LFS'), ('DISK_SPIC_GPFS', 'Disk Storage - SPIC GPFS'), ('HDD', 'HDD'), ('CD', 'CD')], default='3592_JA', max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='taperequest',
            name='output_media',
            field=models.CharField(choices=[('3592_JA', '3592 - JA'), ('3592_JB', '3592 - JB'), ('3592_JC', '3592 - JC'), ('3592_JD', '3592 - JD'), ('3592_JE', '3592 - JE'), ('LTO_1', 'LTO - 1'), ('LTO_2', 'LTO - 2'), ('LTO_3', 'LTO - 3'), ('LTO_4', 'LTO - 4'), ('LTO_5', 'LTO - 5'), ('LTO_6', 'LTO - 6'), ('LTO_7', 'LTO - 7'), ('LTO_8', 'LTO - 8'), ('LTO_9', 'LTO - 9'), ('DISK_SPIC_LFS', 'Disk Storage - SPIC LFS'), ('DISK_SPIC_GPFS', 'Disk Storage - SPIC GPFS'), ('HDD', 'HDD'), ('CD', 'CD')], default='3592_JA', max_length=50),
            preserve_default=False,
        ),
    ]
