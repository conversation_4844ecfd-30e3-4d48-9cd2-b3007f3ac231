# Generated manually for cascading media fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tapemanagement', '0003_taperequest_input_media_taperequest_output_media'),
    ]

    operations = [
        # Remove old media fields
        migrations.RemoveField(
            model_name='taperequest',
            name='input_media',
        ),
        migrations.RemoveField(
            model_name='taperequest',
            name='output_media',
        ),
        # Add new cascading media fields
        migrations.AddField(
            model_name='taperequest',
            name='input_media_type',
            field=models.CharField(choices=[('3592', '3592'), ('LTO', 'LTO'), ('DISK_STORAGE', 'Disk Storage'), ('HDD', 'HDD'), ('CD', 'CD')], default='3592', max_length=20),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='taperequest',
            name='input_media_subtype',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='taperequest',
            name='output_media_type',
            field=models.CharField(choices=[('3592', '3592'), ('LTO', 'LTO'), ('DISK_STORAGE', 'Disk Storage'), ('HDD', 'HDD'), ('CD', 'CD')], default='3592', max_length=20),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='taperequest',
            name='output_media_subtype',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
    ]
